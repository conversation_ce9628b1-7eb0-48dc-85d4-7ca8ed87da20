'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Search, Users, TrendingUp, DollarSign, Eye } from 'lucide-react';
import { GameData, RegionLanguageData, InfluencerData } from '../GameMarketingWizard';
import { findMatchingInfluencers } from '@/data/mockInfluencers';

interface InfluencerMatchingStepProps {
  gameData: GameData;
  regionLanguageData: RegionLanguageData;
  onInfluencersFound: (influencers: InfluencerData[]) => void;
  onNext: () => void;
  onPrev: () => void;
}

export function InfluencerMatchingStep({
  gameData,
  regionLanguageData,
  onInfluencersFound,
  onNext,
  onPrev
}: InfluencerMatchingStepProps) {
  const [isSearching, setIsSearching] = useState(false);
  const [searchComplete, setSearchComplete] = useState(false);
  const [matchedInfluencers, setMatchedInfluencers] = useState<InfluencerData[]>([]);
  const [searchProgress, setSearchProgress] = useState(0);

  const startSearch = async () => {
    setIsSearching(true);
    setSearchProgress(0);
    setSearchComplete(false);

    // Simulate AI search with progress updates
    const progressSteps = [
      { progress: 20, message: 'Analyzing game genre and target audience...' },
      { progress: 40, message: 'Scanning influencer database...' },
      { progress: 60, message: 'Matching regional preferences...' },
      { progress: 80, message: 'Filtering by language requirements...' },
      { progress: 100, message: 'Ranking by engagement and relevance...' }
    ];

    for (const step of progressSteps) {
      await new Promise(resolve => setTimeout(resolve, 800));
      setSearchProgress(step.progress);
    }

    // Perform actual matching
    const results = findMatchingInfluencers(
      gameData.genre,
      regionLanguageData.regions,
      regionLanguageData.languages,
      regionLanguageData.requirements
    );

    setMatchedInfluencers(results);
    onInfluencersFound(results);
    setIsSearching(false);
    setSearchComplete(true);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(0)}K`;
    }
    return num.toString();
  };

  const handleNext = () => {
    if (searchComplete && matchedInfluencers.length > 0) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mb-4">
          <Search className="w-8 h-8 text-purple-600 dark:text-purple-400" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          AI Influencer Matching
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Our AI will find the perfect influencers for your game
        </p>
      </div>

      {/* Search Summary */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Search Criteria
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">Game Genre</div>
            <div className="font-medium text-gray-900 dark:text-white">{gameData.genre}</div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">Target Audience</div>
            <div className="font-medium text-gray-900 dark:text-white">{gameData.targetAudience}</div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">Regions</div>
            <div className="font-medium text-gray-900 dark:text-white">
              {regionLanguageData.regions.join(', ')}
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">Languages</div>
            <div className="font-medium text-gray-900 dark:text-white">
              {regionLanguageData.languages.join(', ')}
            </div>
          </div>
        </div>
      </div>

      {/* Search Button or Progress */}
      {!isSearching && !searchComplete && (
        <div className="text-center">
          <button
            onClick={startSearch}
            className="inline-flex items-center px-8 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors text-lg font-medium"
          >
            <Search className="mr-3 w-5 h-5" />
            Start AI Matching
          </button>
        </div>
      )}

      {/* Search Progress */}
      {isSearching && (
        <div className="text-center space-y-4">
          <div className="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700">
            <div
              className="bg-purple-600 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${searchProgress}%` }}
            ></div>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Searching for influencers... {searchProgress}%
          </p>
        </div>
      )}

      {/* Search Results */}
      {searchComplete && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
              Found {matchedInfluencers.length} Matching Influencers
            </h3>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Sorted by relevance and engagement
            </div>
          </div>

          {matchedInfluencers.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto w-12 h-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No matches found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Try adjusting your criteria or expanding your target regions/languages
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {matchedInfluencers.slice(0, 9).map((influencer) => (
                <div
                  key={influencer.id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow p-6"
                >
                  <div className="flex items-center mb-4">
                    <img
                      src={influencer.avatar}
                      alt={influencer.name}
                      className="w-12 h-12 rounded-full mr-4"
                    />
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {influencer.name}
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {influencer.platform}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Followers</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {formatNumber(influencer.followers)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Avg Views</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {formatNumber(influencer.avgViews)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Engagement</span>
                      <span className="font-medium text-green-600">
                        {influencer.engagementRate}%
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Cost/Post</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        ${influencer.costPerPost.toLocaleString()}
                      </span>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">
                        {influencer.region} • {influencer.language}
                      </span>
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                        {influencer.genre}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6">
        <button
          onClick={onPrev}
          className="flex items-center px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
        >
          <ChevronLeft className="mr-2 w-4 h-4" />
          Back
        </button>
        <button
          onClick={handleNext}
          disabled={!searchComplete || matchedInfluencers.length === 0}
          className="flex items-center px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
          <ChevronRight className="ml-2 w-4 h-4" />
        </button>
      </div>
    </div>
  );
}

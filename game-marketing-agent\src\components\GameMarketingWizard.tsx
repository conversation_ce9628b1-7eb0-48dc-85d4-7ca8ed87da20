'use client';

import { useState } from 'react';
import { ChevronLeft, ChevronRight, Check } from 'lucide-react';
import { GameDetailsStep } from './steps/GameDetailsStep';
import { RegionLanguageStep } from './steps/RegionLanguageStep';
import { InfluencerMatchingStep } from './steps/InfluencerMatchingStep';
import { SampleFeedbackStep } from './steps/SampleFeedbackStep';
import { BudgetMatchingStep } from './steps/BudgetMatchingStep';

export interface GameData {
  title: string;
  genre: string;
  targetAudience: string;
  description: string;
  platforms: string[];
  releaseDate: string;
}

export interface RegionLanguageData {
  regions: string[];
  languages: string[];
  requirements: string;
}

export interface InfluencerData {
  id: string;
  name: string;
  platform: string;
  followers: number;
  avgViews: number;
  genre: string;
  region: string;
  language: string;
  engagementRate: number;
  costPerPost: number;
  avatar: string;
}

const steps = [
  { id: 1, title: 'Game Details', description: 'Tell us about your game' },
  { id: 2, title: 'Region & Language', description: 'Select target markets' },
  { id: 3, title: 'AI Matching', description: 'Find influencers' },
  { id: 4, title: 'Sample Feedback', description: 'Refine matches' },
  { id: 5, title: 'Budget & Results', description: 'Final selection' },
];

export function GameMarketingWizard() {
  const [currentStep, setCurrentStep] = useState(1);
  const [gameData, setGameData] = useState<GameData>({
    title: '',
    genre: '',
    targetAudience: '',
    description: '',
    platforms: [],
    releaseDate: '',
  });
  const [regionLanguageData, setRegionLanguageData] = useState<RegionLanguageData>({
    regions: [],
    languages: [],
    requirements: '',
  });
  const [matchedInfluencers, setMatchedInfluencers] = useState<InfluencerData[]>([]);
  const [selectedInfluencers, setSelectedInfluencers] = useState<InfluencerData[]>([]);
  const [budget, setBudget] = useState<number>(0);

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <GameDetailsStep
            data={gameData}
            onUpdate={setGameData}
            onNext={nextStep}
          />
        );
      case 2:
        return (
          <RegionLanguageStep
            data={regionLanguageData}
            onUpdate={setRegionLanguageData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 3:
        return (
          <InfluencerMatchingStep
            gameData={gameData}
            regionLanguageData={regionLanguageData}
            onInfluencersFound={setMatchedInfluencers}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 4:
        return (
          <SampleFeedbackStep
            influencers={matchedInfluencers}
            onSelectionUpdate={setSelectedInfluencers}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 5:
        return (
          <BudgetMatchingStep
            selectedInfluencers={selectedInfluencers}
            budget={budget}
            onBudgetUpdate={setBudget}
            onPrev={prevStep}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
      {/* Progress Bar */}
      <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep > step.id
                    ? 'bg-green-500 text-white'
                    : currentStep === step.id
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-300 text-gray-600'
                }`}
              >
                {currentStep > step.id ? <Check size={16} /> : step.id}
              </div>
              <div className="ml-3 hidden sm:block">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {step.title}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {step.description}
                </p>
              </div>
              {index < steps.length - 1 && (
                <div className="hidden sm:block w-16 h-0.5 bg-gray-300 mx-4" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="p-6">
        {renderStep()}
      </div>
    </div>
  );
}

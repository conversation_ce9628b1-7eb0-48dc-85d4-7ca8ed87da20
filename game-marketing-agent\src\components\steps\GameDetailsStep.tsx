'use client';

import { useState } from 'react';
import { ChevronRight, Gamepad2 } from 'lucide-react';
import { GameData } from '../GameMarketingWizard';

interface GameDetailsStepProps {
  data: GameData;
  onUpdate: (data: GameData) => void;
  onNext: () => void;
}

const gameGenres = [
  'Action', 'Adventure', 'RPG', 'Strategy', 'Simulation', 'Sports',
  'Racing', 'Puzzle', 'Fighting', 'Shooter', 'Horror', 'Platformer',
  'MMORPG', 'Battle Royale', 'Indie', 'Casual'
];

const platforms = [
  'PC (Steam)', 'PC (Epic Games)', 'PlayStation 5', 'PlayStation 4',
  'Xbox Series X/S', 'Xbox One', 'Nintendo Switch', 'Mobile (iOS)',
  'Mobile (Android)', 'VR'
];

const targetAudiences = [
  'Kids (6-12)', 'Teens (13-17)', 'Young Adults (18-25)', 'Adults (26-35)',
  'Mature Adults (36-50)', 'All Ages', 'Hardcore Gamers', 'Casual Gamers',
  'Competitive Players', 'Story-driven Players'
];

export function GameDetailsStep({ data, onUpdate, onNext }: GameDetailsStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: keyof GameData, value: string | string[]) => {
    onUpdate({ ...data, [field]: value });
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!data.title.trim()) {
      newErrors.title = 'Game title is required';
    }
    if (!data.genre) {
      newErrors.genre = 'Please select a genre';
    }
    if (!data.targetAudience) {
      newErrors.targetAudience = 'Please select target audience';
    }
    if (!data.description.trim()) {
      newErrors.description = 'Game description is required';
    }
    if (data.platforms.length === 0) {
      newErrors.platforms = 'Please select at least one platform';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  const handlePlatformToggle = (platform: string) => {
    const updatedPlatforms = data.platforms.includes(platform)
      ? data.platforms.filter(p => p !== platform)
      : [...data.platforms, platform];
    handleInputChange('platforms', updatedPlatforms);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
          <Gamepad2 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Tell us about your game
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Provide details about your game to help us find the perfect influencers
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Game Title */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Game Title *
          </label>
          <input
            type="text"
            value={data.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.title ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your game title"
          />
          {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
        </div>

        {/* Genre */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Genre *
          </label>
          <select
            value={data.genre}
            onChange={(e) => handleInputChange('genre', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.genre ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select a genre</option>
            {gameGenres.map((genre) => (
              <option key={genre} value={genre}>
                {genre}
              </option>
            ))}
          </select>
          {errors.genre && <p className="mt-1 text-sm text-red-600">{errors.genre}</p>}
        </div>

        {/* Target Audience */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Target Audience *
          </label>
          <select
            value={data.targetAudience}
            onChange={(e) => handleInputChange('targetAudience', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.targetAudience ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value="">Select target audience</option>
            {targetAudiences.map((audience) => (
              <option key={audience} value={audience}>
                {audience}
              </option>
            ))}
          </select>
          {errors.targetAudience && <p className="mt-1 text-sm text-red-600">{errors.targetAudience}</p>}
        </div>

        {/* Release Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Release Date
          </label>
          <input
            type="date"
            value={data.releaseDate}
            onChange={(e) => handleInputChange('releaseDate', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        {/* Description */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Game Description *
          </label>
          <textarea
            value={data.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.description ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Describe your game, its unique features, and what makes it special..."
          />
          {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
        </div>

        {/* Platforms */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Platforms *
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {platforms.map((platform) => (
              <label
                key={platform}
                className={`flex items-center p-3 border rounded-md cursor-pointer transition-colors ${
                  data.platforms.includes(platform)
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 hover:border-gray-400 dark:border-gray-600'
                }`}
              >
                <input
                  type="checkbox"
                  checked={data.platforms.includes(platform)}
                  onChange={() => handlePlatformToggle(platform)}
                  className="sr-only"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {platform}
                </span>
              </label>
            ))}
          </div>
          {errors.platforms && <p className="mt-1 text-sm text-red-600">{errors.platforms}</p>}
        </div>
      </div>

      {/* Next Button */}
      <div className="flex justify-end pt-6">
        <button
          onClick={handleNext}
          className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        >
          Continue
          <ChevronRight className="ml-2 w-4 h-4" />
        </button>
      </div>
    </div>
  );
}

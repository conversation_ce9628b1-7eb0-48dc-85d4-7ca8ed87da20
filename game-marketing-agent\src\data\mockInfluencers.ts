import { InfluencerData } from '@/components/GameMarketingWizard';

export const mockInfluencers: InfluencerData[] = [
  // Action Game Influencers
  {
    id: '1',
    name: 'ActionMaster_TV',
    platform: 'Twitch',
    followers: 850000,
    avgViews: 45000,
    genre: 'Action',
    region: 'NA',
    language: 'English',
    engagementRate: 8.5,
    costPerPost: 5000,
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '2',
    name: 'Gamer<PERSON>irl_<PERSON>',
    platform: 'YouTube',
    followers: 1200000,
    avgViews: 120000,
    genre: 'Action',
    region: 'EU',
    language: 'English',
    engagementRate: 9.2,
    costPerPost: 8000,
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b5c5?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON><PERSON>r_<PERSON>',
    platform: 'Twitch',
    followers: 650000,
    avgViews: 35000,
    genre: 'Action',
    region: 'ASIA',
    language: 'Japanese',
    engagementRate: 7.8,
    costPerPost: 4500,
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
  },

  // RPG Game Influencers
  {
    id: '4',
    name: 'RPGLegend_Mike',
    platform: 'YouTube',
    followers: 950000,
    avgViews: 85000,
    genre: 'RPG',
    region: 'NA',
    language: 'English',
    engagementRate: 8.9,
    costPerPost: 6500,
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '5',
    name: 'FantasyQueen_EU',
    platform: 'Twitch',
    followers: 720000,
    avgViews: 42000,
    genre: 'RPG',
    region: 'EU',
    language: 'German',
    engagementRate: 9.1,
    costPerPost: 5500,
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '6',
    name: 'DragonSlayer_KR',
    platform: 'YouTube',
    followers: 1100000,
    avgViews: 95000,
    genre: 'RPG',
    region: 'ASIA',
    language: 'Korean',
    engagementRate: 8.7,
    costPerPost: 7000,
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face'
  },

  // Strategy Game Influencers
  {
    id: '7',
    name: 'StrategyMind_Pro',
    platform: 'YouTube',
    followers: 680000,
    avgViews: 55000,
    genre: 'Strategy',
    region: 'NA',
    language: 'English',
    engagementRate: 8.3,
    costPerPost: 4800,
    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '8',
    name: 'TacticalGamer_FR',
    platform: 'Twitch',
    followers: 540000,
    avgViews: 32000,
    genre: 'Strategy',
    region: 'EU',
    language: 'French',
    engagementRate: 7.9,
    costPerPost: 4200,
    avatar: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=100&h=100&fit=crop&crop=face'
  },

  // Shooter Game Influencers
  {
    id: '9',
    name: 'HeadshotKing_US',
    platform: 'Twitch',
    followers: 1500000,
    avgViews: 180000,
    genre: 'Shooter',
    region: 'NA',
    language: 'English',
    engagementRate: 9.5,
    costPerPost: 12000,
    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '10',
    name: 'SniperQueen_BR',
    platform: 'YouTube',
    followers: 890000,
    avgViews: 78000,
    genre: 'Shooter',
    region: 'SA',
    language: 'Portuguese',
    engagementRate: 8.6,
    costPerPost: 6200,
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face'
  },

  // Horror Game Influencers
  {
    id: '11',
    name: 'ScreamMaster_UK',
    platform: 'YouTube',
    followers: 750000,
    avgViews: 65000,
    genre: 'Horror',
    region: 'EU',
    language: 'English',
    engagementRate: 9.3,
    costPerPost: 5800,
    avatar: 'https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '12',
    name: 'NightmareGamer_DE',
    platform: 'Twitch',
    followers: 620000,
    avgViews: 38000,
    genre: 'Horror',
    region: 'EU',
    language: 'German',
    engagementRate: 8.8,
    costPerPost: 4900,
    avatar: 'https://images.unsplash.com/photo-1463453091185-61582044d556?w=100&h=100&fit=crop&crop=face'
  },

  // Indie Game Influencers
  {
    id: '13',
    name: 'IndieExplorer_CA',
    platform: 'YouTube',
    followers: 420000,
    avgViews: 35000,
    genre: 'Indie',
    region: 'NA',
    language: 'English',
    engagementRate: 9.7,
    costPerPost: 3500,
    avatar: 'https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '14',
    name: 'PixelArtist_JP',
    platform: 'Twitch',
    followers: 380000,
    avgViews: 28000,
    genre: 'Indie',
    region: 'ASIA',
    language: 'Japanese',
    engagementRate: 9.0,
    costPerPost: 3200,
    avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=100&h=100&fit=crop&crop=face'
  },

  // Battle Royale Influencers
  {
    id: '15',
    name: 'BattleRoyalePro_US',
    platform: 'Twitch',
    followers: 2100000,
    avgViews: 250000,
    genre: 'Battle Royale',
    region: 'NA',
    language: 'English',
    engagementRate: 9.8,
    costPerPost: 15000,
    avatar: 'https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '16',
    name: 'VictoryRoyale_ES',
    platform: 'YouTube',
    followers: 1300000,
    avgViews: 140000,
    genre: 'Battle Royale',
    region: 'EU',
    language: 'Spanish',
    engagementRate: 9.1,
    costPerPost: 9500,
    avatar: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=100&h=100&fit=crop&crop=face'
  },

  // Simulation Game Influencers
  {
    id: '17',
    name: 'SimBuilder_AU',
    platform: 'YouTube',
    followers: 580000,
    avgViews: 48000,
    genre: 'Simulation',
    region: 'ASIA',
    language: 'English',
    engagementRate: 8.4,
    costPerPost: 4300,
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '18',
    name: 'CityPlanner_NL',
    platform: 'Twitch',
    followers: 450000,
    avgViews: 32000,
    genre: 'Simulation',
    region: 'EU',
    language: 'Dutch',
    engagementRate: 8.1,
    costPerPost: 3800,
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b5c5?w=100&h=100&fit=crop&crop=face'
  },

  // Sports Game Influencers
  {
    id: '19',
    name: 'SportsGamer_UK',
    platform: 'YouTube',
    followers: 920000,
    avgViews: 82000,
    genre: 'Sports',
    region: 'EU',
    language: 'English',
    engagementRate: 8.7,
    costPerPost: 6800,
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '20',
    name: 'FIFAMaster_IT',
    platform: 'Twitch',
    followers: 670000,
    avgViews: 45000,
    genre: 'Sports',
    region: 'EU',
    language: 'Italian',
    engagementRate: 8.2,
    costPerPost: 5200,
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
  }
];

// Helper function to filter influencers based on criteria
export function findMatchingInfluencers(
  genre: string,
  regions: string[],
  languages: string[],
  requirements?: string
): InfluencerData[] {
  let filtered = mockInfluencers.filter(influencer => {
    // Match genre
    const genreMatch = influencer.genre.toLowerCase() === genre.toLowerCase();
    
    // Match region
    const regionMatch = regions.length === 0 || regions.includes(influencer.region);
    
    // Match language
    const languageMatch = languages.length === 0 || languages.includes(influencer.language);
    
    return genreMatch && regionMatch && languageMatch;
  });

  // Apply additional requirements filtering (simple keyword matching)
  if (requirements && requirements.trim()) {
    const keywords = requirements.toLowerCase().split(' ');
    filtered = filtered.filter(influencer => {
      const searchText = `${influencer.name} ${influencer.platform} ${influencer.genre}`.toLowerCase();
      return keywords.some(keyword => searchText.includes(keyword));
    });
  }

  // Sort by engagement rate and followers
  return filtered.sort((a, b) => {
    const scoreA = (a.engagementRate * 0.6) + (a.followers / 1000000 * 0.4);
    const scoreB = (b.engagementRate * 0.6) + (b.followers / 1000000 * 0.4);
    return scoreB - scoreA;
  });
}

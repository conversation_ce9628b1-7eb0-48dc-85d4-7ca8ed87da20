'use client';

import { useState } from 'react';
import { ChevronLeft, ChevronRight, Globe, MessageSquare } from 'lucide-react';
import { RegionLanguageData } from '../GameMarketingWizard';

interface RegionLanguageStepProps {
  data: RegionLanguageData;
  onUpdate: (data: RegionLanguageData) => void;
  onNext: () => void;
  onPrev: () => void;
}

const regions = [
  { code: 'NA', name: 'North America', countries: ['United States', 'Canada', 'Mexico'] },
  { code: 'EU', name: 'Europe', countries: ['United Kingdom', 'Germany', 'France', 'Spain', 'Italy'] },
  { code: 'ASIA', name: 'Asia Pacific', countries: ['Japan', 'South Korea', 'China', 'Australia', 'India'] },
  { code: 'SA', name: 'South America', countries: ['Brazil', 'Argentina', 'Chile'] },
  { code: 'MEA', name: 'Middle East & Africa', countries: ['UAE', 'Saudi Arabia', 'South Africa'] },
];

const languages = [
  'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese',
  'Japanese', 'Korean', 'Chinese (Simplified)', 'Chinese (Traditional)',
  'Russian', 'Arabic', 'Hindi', 'Dutch', 'Swedish', 'Polish'
];

export function RegionLanguageStep({ data, onUpdate, onNext, onPrev }: RegionLanguageStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleRegionToggle = (regionCode: string) => {
    const updatedRegions = data.regions.includes(regionCode)
      ? data.regions.filter(r => r !== regionCode)
      : [...data.regions, regionCode];
    onUpdate({ ...data, regions: updatedRegions });
    
    if (errors.regions) {
      setErrors({ ...errors, regions: '' });
    }
  };

  const handleLanguageToggle = (language: string) => {
    const updatedLanguages = data.languages.includes(language)
      ? data.languages.filter(l => l !== language)
      : [...data.languages, language];
    onUpdate({ ...data, languages: updatedLanguages });
    
    if (errors.languages) {
      setErrors({ ...errors, languages: '' });
    }
  };

  const handleRequirementsChange = (requirements: string) => {
    onUpdate({ ...data, requirements });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (data.regions.length === 0) {
      newErrors.regions = 'Please select at least one region';
    }
    if (data.languages.length === 0) {
      newErrors.languages = 'Please select at least one language';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4">
          <Globe className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Select Target Markets
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Choose the regions and languages for your marketing campaign
        </p>
      </div>

      {/* Regions */}
      <div>
        <label className="block text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">
          Target Regions *
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {regions.map((region) => (
            <div
              key={region.code}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                data.regions.includes(region.code)
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md'
                  : 'border-gray-300 hover:border-gray-400 dark:border-gray-600 hover:shadow-sm'
              }`}
              onClick={() => handleRegionToggle(region.code)}
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  {region.name}
                </h3>
                <input
                  type="checkbox"
                  checked={data.regions.includes(region.code)}
                  onChange={() => handleRegionToggle(region.code)}
                  className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                />
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {region.countries.slice(0, 3).join(', ')}
                {region.countries.length > 3 && '...'}
              </p>
            </div>
          ))}
        </div>
        {errors.regions && <p className="mt-2 text-sm text-red-600">{errors.regions}</p>}
      </div>

      {/* Languages */}
      <div>
        <label className="block text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">
          Target Languages *
        </label>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {languages.map((language) => (
            <label
              key={language}
              className={`flex items-center p-3 border rounded-md cursor-pointer transition-colors ${
                data.languages.includes(language)
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-300 hover:border-gray-400 dark:border-gray-600'
              }`}
            >
              <input
                type="checkbox"
                checked={data.languages.includes(language)}
                onChange={() => handleLanguageToggle(language)}
                className="sr-only"
              />
              <MessageSquare className="w-4 h-4 mr-2 text-gray-500" />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {language}
              </span>
            </label>
          ))}
        </div>
        {errors.languages && <p className="mt-2 text-sm text-red-600">{errors.languages}</p>}
      </div>

      {/* Additional Requirements */}
      <div>
        <label className="block text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">
          Additional Requirements
        </label>
        <textarea
          value={data.requirements}
          onChange={(e) => handleRequirementsChange(e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          placeholder="Specify any additional requirements for influencers (e.g., minimum followers, specific content style, gaming experience, etc.)"
        />
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          This will help our AI find influencers that better match your specific needs
        </p>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6">
        <button
          onClick={onPrev}
          className="flex items-center px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
        >
          <ChevronLeft className="mr-2 w-4 h-4" />
          Back
        </button>
        <button
          onClick={handleNext}
          className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        >
          Continue
          <ChevronRight className="ml-2 w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
